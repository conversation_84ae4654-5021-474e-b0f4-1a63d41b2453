'use client'

import { useTheme } from "next-themes"

const React = require('react')

export function SealogsDashboardIcon({ ...props }) {
//    return /*#__PURE__*/ React.createElement(
//    'svg',
//    Object.assign(
//      {
//        id: 'a',
//        dataname: 'Layer 1',
//        viewBox: '0 0 117.71 121.34',
//      },
//      props,
//    ),
//    /*#__PURE__*/ React.createElement('path', {
//      d: 'M72.12,110.81h-17.18v-14.54c0-5.95-5.95-10.57-13.22-10.57s-13.22,4.63-13.22,10.57v14.54H11.33l11.89-48.24h37l11.89,48.24h0Z',
//      fill: '#f4f5f7',
//      stroke: '#0c2745',
//      strokeMiterlimit: '10',
//      strokeWidth: '2px',
//    }),
//    /*#__PURE__*/ React.createElement('path', {
//      d: 'M23.39,46.02v11.63H5.05v8.57h17.71l-11.38,44.7H.62v9.8h82.2v-9.8h-10.75l-11.38-44.7h17.71v-8.57h-18.34v-11.63',
//      fill: '#fff',
//      stroke: '#0c2745',
//      strokeMiterlimit: '10',
//      strokeWidth: '2px',
//    }),
//    /*#__PURE__*/ React.createElement('path', {
//      d: 'M29.83,110.81v-14.54c0-5.29,5.29-9.25,11.89-9.25s11.89,3.96,11.89,9.25v14.54h-23.79ZM11.33,25.91L41.72.8l31.06,25.11H11.33Z',
//      fill: '#fff',
//      stroke: '#0c2745',
//      strokeMiterlimit: '10',
//      strokeWidth: '2px',
//    }),
//    /*#__PURE__*/ React.createElement('polygon', {
//      points: '117.05 28.87 117.05 50.68 63.53 39.45 117.05 28.87',
//      fill: 'none',
//      stroke: '#0c2745',
//      strokeMiterlimit: '10',
//      strokeWidth: '2px',
//    }),
//    /*#__PURE__*/ React.createElement('rect', {
//      x: '23.22',
//      y: '26.06',
//      width: '37',
//      height: '31.5',
//      fill: 'none',
//      stroke: '#0c2745',
//      strokeMiterlimit: '10',
//      strokeWidth: '2px',
//    }),
//  )
  const { theme, setTheme } = useTheme()

  const getTheme = () => {
    switch (theme) {
      case 'dark':
        return 'Dark'
      case 'light':
        return 'Light'
      default:
        return 'System'
    }
  }

  getTheme()

  if (theme === 'light') {
    return React.createElement('img', {
      src: '/sealogs-V2_Icons/125px_icon/Lighthouse-light.svg',
      alt: 'Home Light Icon',
      ...props,
    });
  } else if (theme === 'dark') {
    return React.createElement('img', {
      src: '/sealogs-V2_Icons/125px_icon/lighthouse-dark.svg',
      alt: 'Home Dark Icon',
      ...props,
    });
  }
}
