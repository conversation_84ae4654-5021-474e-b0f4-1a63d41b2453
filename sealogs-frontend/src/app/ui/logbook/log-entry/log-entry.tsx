'use client'

import LogDate from '../log-date'
import MasterList from '../master'
import { useEffect, useMemo, useState } from 'react'
import Link from 'next/link'
import { usePathname, useRouter, useSearchParams } from 'next/navigation'
import dayjs from 'dayjs'
import { getPermissions, hasPermission } from '@/app/helpers/userHelper'

import OpenPreviousLogbookComments from '../open-previous-comments'
import VesselModel from '@/app/offline/models/vessel'
import CrewMembers_LogBookEntrySectionModel from '@/app/offline/models/crewMembers_LogBookEntrySection'
import LogBookEntryModel from '@/app/offline/models/logBookEntry'
import CustomisedLogBookConfigModel from '@/app/offline/models/customisedLogBookConfig'
import SeaLogsMemberModel from '@/app/offline/models/seaLogsMember'
import SectionMemberCommentModel from '@/app/offline/models/sectionMemberComment'
import AssetReporting_LogBookEntrySectionModel from '@/app/offline/models/assetReporting_LogBookEntrySection'
import Fuel_LogBookEntrySectionModel from '@/app/offline/models/fuel_LogBookEntrySection'
import Ports_LogBookEntrySectionModel from '@/app/offline/models/ports_LogBookEntrySection'
import Supernumerary_LogBookEntrySectionModel from '@/app/offline/models/supernumerary_LogBookEntrySection'
import TripReport_LogBookEntrySectionModel from '@/app/offline/models/tripReport_LogBookEntrySection'
import VesselDailyCheck_LogBookEntrySectionModel from '@/app/offline/models/vesselDailyCheck_LogBookEntrySection'
import VoyageSummary_LogBookEntrySectionModel from '@/app/offline/models/voyageSummary_LogBookEntrySection'
import CrewWelfare_LogBookEntrySectionModel from '@/app/offline/models/crewWelfare_LogBookEntrySection'
import LogBookSignOff_LogBookEntrySectionModel from '@/app/offline/models/logBookSignOff_LogBookEntrySection'
import VehiclePositionModel from '@/app/offline/models/vehiclePosition'
import { generateUniqueId } from '@/app/offline/helpers/functions'
import GeoLocationModel from '@/app/offline/models/geoLocation'
import { formatDate, isVesselArrivalLate } from '@/app/helpers/dateHelper'
import EventType_SupernumeraryModel from '@/app/offline/models/eventType_Supernumerary'
import EventType_PassengerDropFacilityModel from '@/app/offline/models/eventType_PassengerDropFacility'
import DangerousGoodsChecklistModel from '@/app/offline/models/dangerousGoodsChecklist'
import TripReport_StopModel from '@/app/offline/models/tripReport_Stop'
import EventType_BarCrossingModel from '@/app/offline/models/eventType_BarCrossing'
import EventType_RestrictedVisibilityModel from '@/app/offline/models/eventType_RestrictedVisibility'
import EventType_TaskingModel from '@/app/offline/models/eventType_Tasking'
import InfringementNoticeModel from '@/app/offline/models/infringementNotice'
import {
    getLogBookEntries,
    GetLogBookEntriesMembers,
    getOneClient,
    getSeaLogsMembersList,
    getVesselByID,
} from '@/app/lib/actions'
import { useLazyQuery, useMutation } from '@apollo/client'
import {
    CREATE_VESSEL_POSITION,
    UPDATE_LOGBOOK_ENTRY,
    DELETE_LOGBOOK_ENTRY,
} from '@/app/lib/graphQL/mutation'
import {
    VesselDailyCheck_LogBookEntrySection,
    Get_AllLogBookEntryOldConfigs,
} from '@/app/lib/graphQL/query'
import { uniqueLogbookComponents } from '@/app/helpers/logBookHelper'
import LogBookEntryOldConfigsModel from '@/app/offline/models/logBookEntryOldConfigs'
import { GetLogBookEntryOldConfigs } from '@/app/lib/graphQL/query/offline/GetLogBookEntryOldConfigs'
import LogEntryMainContent from '../log-entry-main-content/log-entry-main-content'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { toast } from 'sonner'
import { AlertDialogNew } from '@/components/ui/alert-dialog-new'

import { Button } from '@/components/ui/button'
import { File, UnlockIcon } from 'lucide-react'
import { format } from 'date-fns'
import { LogbookActionMenu } from '../components/logbook-dropdown'
import VesselIcon from '../../vessels/vesel-icon'
import { ListHeader } from '@/components/ui/list-header'
import RadioLogs from '../radio-logs'
import { cn } from '@/app/lib/utils'
import {
    CreateAppNotification,
    ReadCrewMembers_LogBookEntrySections,
    ReadCrewWelfare_LogBookEntrySections,
    ReadFuel_LogBookEntrySections,
    ReadLogBookSignOff_LogBookEntrySections,
    ReadOneCustomisedLogBookConfig,
    ReadOneLogBookEntry,
    ReadSectionMemberComments,
    ReadSupernumerary_LogBookEntrySections,
    ReadTripReport_LogBookEntrySections,
    ReadVesselDailyCheck_LogBookEntrySections,
} from './queries'

export default function LogBookEntry({
    vesselID,
    logentryID,
}: {
    vesselID: number
    logentryID: number
}) {
    const pathname = usePathname() // "/log-entries"
    const searchParams = useSearchParams() // URLSearchParams
    const [isAdmin, setIsAdmin] = useState(false)
    const [loaded, setLoaded] = useState(false)
    const [logbook, setLogbook] = useState<any>(false)
    const [lbeVersions, setLbeVersions] = useState<any>([])
    const [logBookConfig, setLogBookConfig] = useState<any>(false)
    const [client, setClient] = useState<any>(false)
    const [vessel, setVessel] = useState<any>()
    const [crew, setCrew] = useState<any>()
    const [locked, setLocked] = useState(false)
    const [logEntrySections, setLogEntrySections] = useState<any>()
    const [startDate, setStartDate] = useState<any>(null)
    const [fuel, setFuel] = useState<any>()
    const [fuelLogs, setFuelLogs] = useState<any>([])
    const [vesselDailyCheck, setVesselDailyCheck] = useState<any>(false)
    const [signOff, setSignOff] = useState<any>(false)
    const [crewWelfare, setCrewWelfare] = useState<any>(false)
    const [tripReport, setTripReport] = useState<any>()
    const [crewMembers, setCrewMembers] = useState<any>(false)
    const [crewMembersList, setCrewMembersList] = useState<any>([])
    // const [crewTraining, setCrewTraining] = useState<any>()
    const [supernumerary, setSupernumerary] = useState<any>()
    const [openLogEntries, setOpenLogEntries] = useState<any>()
    const router = useRouter()
    const [masterID, setMasterID] = useState(0)
    const [prevComments, setPrevComments] = useState([] as any)
    const [deleteConfirmation, setDeleteConfirmation] = useState(false)
    const [lastSnapshot, setLastSnapshot] = useState<any>(false)
    const [open, setOpen] = useState<any>(false)
    const [openCreateTaskSidebar, setOpenCreateTaskSidebar] = useState(false)
    const [forecast, setForecast] = useState({} as any)
    const [isWriteModeForecast, setIsWriteModeForecast] = useState(false)
    const [openTripSelectionDialog, setOpenTripSelectionDialog] =
        useState(false)
    const [doCreateTripReport, setdDoCreateTripReport] = useState<any>()
    const offline = false
    const cmlbsModel = new CrewMembers_LogBookEntrySectionModel()
    const logbookModel = new LogBookEntryModel()
    const clbcModel = new CustomisedLogBookConfigModel()
    const slmModel = new SeaLogsMemberModel()
    const smcModel = new SectionMemberCommentModel()
    const arlbesModel = new AssetReporting_LogBookEntrySectionModel()
    const fuelModel = new Fuel_LogBookEntrySectionModel()
    const portModel = new Ports_LogBookEntrySectionModel()
    const supernumeraryModel = new Supernumerary_LogBookEntrySectionModel()
    const tripReportModel = new TripReport_LogBookEntrySectionModel()
    const vesselDailyCheckModel =
        new VesselDailyCheck_LogBookEntrySectionModel()
    const voyageSummaryModel = new VoyageSummary_LogBookEntrySectionModel()
    const crewWelfareModel = new CrewWelfare_LogBookEntrySectionModel()
    const signOffModel = new LogBookSignOff_LogBookEntrySectionModel()
    const vehiclePositionModel = new VehiclePositionModel()
    const geoLocationModel = new GeoLocationModel()
    const vesselModel = new VesselModel()
    const supernumeraryEventModel = new EventType_SupernumeraryModel()
    const passengerDropFacilityModel =
        new EventType_PassengerDropFacilityModel()
    const dangerousGoodsChecklistModel = new DangerousGoodsChecklistModel()
    const tripReport_StopModel = new TripReport_StopModel()
    const barCrossingModel = new EventType_BarCrossingModel()
    const restrictedVisibilityModel = new EventType_RestrictedVisibilityModel()
    const taskingModel = new EventType_TaskingModel()
    const infringementNoticeModel = new InfringementNoticeModel()
    const logBookEntryOldConfigsModel = new LogBookEntryOldConfigsModel()

    const handleSetLogbooks = (logbooks: any) => {
        setOpenLogEntries(
            logbooks.filter((entry: any) => entry.state !== 'Locked'),
        )

        // Sort logbook entries
        const sortedLogbooks = logbooks.sort(
            (a: any, b: any) => parseInt(b.id) - parseInt(a.id),
        )
        // Get previous log entries
        const prevLogbooks = sortedLogbooks.filter(
            (logbook: any) =>
                parseInt(logbook.id) < logentryID && logbook.state == 'Locked',
        )
        if (prevLogbooks.length > 0) {
            handleSetPrevLogbooks(prevLogbooks)
        }
    }

    getLogBookEntries(vesselID, handleSetLogbooks, offline)
    // const getLogBookEntries = async () => {
    //     const crew = await cmlbsModel.getAll()
    //     const entries = await logbookModel.getByVesselId(vesselID)
    //     const data = entries.map((entry: any) => {
    //         const crewData = crew.filter(
    //             (crewMember: any) => crewMember.logBookEntryID === entry.id,
    //         )
    //         return {
    //             ...entry,
    //             crew: crewData,
    //         }
    //     })
    //     if (data) {
    //         handleSetLogbooks(data)
    //     }
    // }

    // const [getSectionEngine_LogBookEntrySection] = useLazyQuery(
    //     ReadEngine_LogBookEntrySections,
    //     {
    //         fetchPolicy: 'cache-and-network',
    //         onCompleted: (response: any) => {
    //             const data = response.readEngine_LogBookEntrySections.nodes
    //             setEngine(data)
    //         },
    //         onError: (error: any) => {
    //             console.error('Engine_LogBookEntrySection error', error)
    //         },
    //     },
    // )

    const [getSectionFuel_LogBookEntrySection] = useLazyQuery(
        ReadFuel_LogBookEntrySections,
        {
            fetchPolicy: 'cache-and-network',
            onCompleted: (response: any) => {
                const data = response.readFuel_LogBookEntrySections.nodes
                setFuel(data)
            },
            onError: (error: any) => {
                console.error('Fuel_LogBookEntrySection error', error)
            },
        },
    )
    // const [getSectionPorts_LogBookEntrySection] = useLazyQuery(
    //     Ports_LogBookEntrySection,
    //     {
    //         fetchPolicy: 'cache-and-network',
    //         onCompleted: (response: any) => {
    //             const data = response.readPorts_LogBookEntrySections.nodes
    //             setPorts(data)
    //         },
    //         onError: (error: any) => {
    //             console.error('Ports_LogBookEntrySection error', error)
    //         },
    //     },
    // )
    const [getSectionVesselDailyCheck_LogBookEntrySection] = useLazyQuery(
        ReadVesselDailyCheck_LogBookEntrySections,
        {
            fetchPolicy: 'cache-and-network',
            onCompleted: (response: any) => {
                const data =
                    response.readVesselDailyCheck_LogBookEntrySections.nodes
                setVesselDailyCheck(data)
            },
            onError: (error: any) => {
                console.error(
                    'VesselDailyCheck_LogBookEntrySection error',
                    error,
                )
            },
        },
    )
    const [getLogBookSignOff_LogBookEntrySection] = useLazyQuery(
        ReadLogBookSignOff_LogBookEntrySections,
        {
            fetchPolicy: 'cache-and-network',
            onCompleted: (response: any) => {
                const data =
                    response.readLogBookSignOff_LogBookEntrySections.nodes
                setSignOff(data)
            },
            onError: (error: any) => {
                console.error('LogBookSignOff_LogBookEntrySection error', error)
            },
        },
    )
    // const [getSectionVoyageSummary_LogBookEntrySection] = useLazyQuery(
    //     VoyageSummary_LogBookEntrySection,
    //     {
    //         fetchPolicy: 'cache-and-network',
    //         onCompleted: (response: any) => {
    //             const data =
    //                 response.readVoyageSummary_LogBookEntrySections.nodes
    //             setVoyageSummary(data)
    //         },
    //         onError: (error: any) => {
    //             console.error('VoyageSummary_LogBookEntrySection error', error)
    //         },
    //     },
    // )
    const [getSectionTripReport_LogBookEntrySection] = useLazyQuery(
        ReadTripReport_LogBookEntrySections,
        {
            fetchPolicy: 'cache-and-network',
            onCompleted: (response: any) => {
                const data = response.readTripReport_LogBookEntrySections.nodes
                setTripReport(data)
                refreshVesselPosition(data)
            },
            onError: (error: any) => {
                console.error('TripReport_LogBookEntrySection error', error)
            },
        },
    )

    const [createVesselPosition] = useMutation(CREATE_VESSEL_POSITION, {
        onError: (error: any) => {
            console.error('Logbook entry update error', error)
        },
    })
    const refreshVesselPosition = async (data: any) => {
        var lat = 0,
            long = 0,
            locationID = 0
        data.forEach((item: any) => {
            if (
                (item.toLat != 0 && item.toLong != 0) ||
                item.toLocationID > 0
            ) {
                lat = item.toLat
                long = item.toLong
                locationID = item.toLocationID
            }
            if (
                (item.fromLat != 0 && item.fromLong != 0) ||
                item.fromLocationID > 0
            ) {
                lat = item.fromLat
                long = item.fromLong
                locationID = item.fromLocationID
            }
        })
        if ((lat != 0 && long != 0) || (locationID && +locationID > 0)) {
            if (vessel) {
                if (
                    vessel.vehiclePositions.nodes?.[0]?.lat != lat ||
                    vessel.vehiclePositions.nodes?.[0]?.long != long ||
                    vessel.vehiclePositions.nodes?.[0]?.geolocation?.id !=
                        locationID
                ) {
                    if (offline) {
                        const id = generateUniqueId()
                        let geoLocation = null
                        if (locationID) {
                            geoLocation =
                                await geoLocationModel.getById(locationID)
                        }
                        const vehicle = await vesselModel.getById(vessel.id)
                        const data = {
                            id: `${id}`,
                            vehicleID: `${vessel.id}`,
                            vehicle: vehicle,
                            lat: lat ? lat : null,
                            long: long ? long : null,
                            geoLocationID: locationID ? `${locationID}` : null,
                            geoLocation: geoLocation,
                        }
                        await vehiclePositionModel.save(data)
                    }
                } else {
                    createVesselPosition({
                        variables: {
                            input: {
                                vehicleID: vessel.id,
                                lat: lat ? lat : null,
                                long: long ? long : null,
                                geoLocationID: locationID ? +locationID : null,
                            },
                        },
                    })
                }
            }
        }
    }

    const [getSectionCrewMembers_LogBookEntrySection] = useLazyQuery(
        ReadCrewMembers_LogBookEntrySections,
        {
            fetchPolicy: 'cache-and-network',
            onCompleted: (response: any) => {
                let data = response.readCrewMembers_LogBookEntrySections.nodes
                setCrewMembers(data)
            },
            onError: (error: any) => {
                console.error('CrewMembers_LogBookEntrySection error', error)
            },
        },
    )

    const handleSetCrewMembers = async (crewMembers: any) => {
        //why this variable not used?
        const crewMemberList = crewMembers.filter(
            (item: any) =>
                !logEntrySections
                    ?.filter(
                        (item: any) =>
                            item.className ===
                            'SeaLogs\\CrewMembers_LogBookEntrySection',
                    )
                    ?.flatMap((item: any) => +item.ids)
                    ?.includes(item),
        )
    }

    const [getSectionCrewWelfare_LogBookEntrySection] = useLazyQuery(
        ReadCrewWelfare_LogBookEntrySections,
        {
            fetchPolicy: 'cache-and-network',
            onCompleted: (response: any) => {
                const data = response.readCrewWelfare_LogBookEntrySections.nodes
                setCrewWelfare(data[0])
            },
            onError: (error: any) => {
                console.error('CrewWelfare_LogBookEntrySection error', error)
            },
        },
    )
    const [queryLogBookConfig] = useLazyQuery(ReadOneCustomisedLogBookConfig, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response: any) => {
            const responseData = response.readOneCustomisedLogBookConfig
            if (responseData) {
                const uniqueComponents = uniqueLogbookComponents(responseData)
                setLogBookConfig({
                    ...responseData,
                    customisedLogBookComponents: { nodes: uniqueComponents },
                })
            }
        },
        onError: (error: any) => {
            console.error('queryLogBookConfig error', error)
        },
    })

    const [queryLogBookEntryOldConfigs] = useLazyQuery(
        Get_AllLogBookEntryOldConfigs,
        {
            fetchPolicy: 'cache-and-network',
            onCompleted: (response: any) => {
                const data = response.readLogBookEntryOldConfigss.nodes
                if (data) {
                    setLbeVersions(data.slice(1))
                    if (data.length > 0) {
                        const lastSnapshot = data[data.length - 1].id
                        setLastSnapshot(lastSnapshot)
                    }
                }
            },
            onError: (error: any) => {
                console.error('queryLogBookEntryOldConfigs error', error)
            },
        },
    )

    const [getLogBookEntryOldConfig] = useLazyQuery(GetLogBookEntryOldConfigs, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response: any) => {
            const data = response.readOneLogBookEntryOldConfigs
            if (data) {
                setLogBookConfig(data)
            }
        },
        onError: (error: any) => {
            console.error('getLogBookEntryOldConfig error', error)
        },
    })

    const offlineQueryLogBookEntryOldConfigs = async () => {
        const data =
            await logBookEntryOldConfigsModel.getByLogBookEntryID(logentryID)
        if (data) {
            setLbeVersions(data.slice(1))
        }
    }
    useEffect(() => {
        if (locked) {
            getLogBookConfig()
        } else if (logbook?.logBook?.id > 0) {
            if (offline) {
                const responseData = clbcModel.getByCustomisedLogBookId(
                    logbook.logBook.id,
                )
                if (responseData) {
                    const uniqueComponents =
                        uniqueLogbookComponents(responseData)
                    setLogBookConfig({
                        ...responseData,
                        customisedLogBookComponents: {
                            nodes: uniqueComponents,
                        },
                    })
                }
            } else {
                queryLogBookConfig({
                    variables: {
                        id: logbook.logBook.id,
                    },
                })
            }
        }
        if (offline) {
            offlineQueryLogBookEntryOldConfigs()
        } else {
            queryLogBookEntryOldConfigs({
                variables: {
                    id: +logentryID,
                },
            })
        }
    }, [locked, logbook])

    // const [getSectionCrewTraining_LogBookEntrySection] = useLazyQuery(
    //     ReadCrewTraining_LogBookEntrySections,
    //     {
    //         fetchPolicy: 'cache-and-network',
    //         onCompleted: (response: any) => {
    //             const data =
    //                 response.readCrewTraining_LogBookEntrySections.nodes
    //             setCrewTraining(data)
    //         },
    //         onError: (error: any) => {
    //             console.error('CrewTraining_LogBookEntrySection error', error)
    //         },
    //     },
    // )
    const [getSectionSupernumerary_LogBookEntrySection] = useLazyQuery(
        ReadSupernumerary_LogBookEntrySections,
        {
            fetchPolicy: 'cache-and-network',
            onCompleted: (response: any) => {
                const data =
                    response.readSupernumerary_LogBookEntrySections.nodes
                setSupernumerary(data)
            },
            onError: (error: any) => {
                console.error('Supernumerary_LogBookEntrySection error', error)
            },
        },
    )
    // const [getSectionEngineer_LogBookEntrySection] = useLazyQuery(
    //     Engineer_LogBookEntrySection,
    //     {
    //         fetchPolicy: 'cache-and-network',
    //         onCompleted: (response: any) => {
    //             const data = response.readEngineer_LogBookEntrySections.nodes
    //             setEngineer(data)
    //         },
    //         onError: (error: any) => {
    //             console.error('Engineer_LogBookEntrySection error', error)
    //         },
    //     },
    // )
    // const [getSectionAssetReporting_LogBookEntrySection] = useLazyQuery(
    //     ReadOneAssetReporting_LogBookEntrySection,
    //     {
    //         fetchPolicy: 'cache-and-network',
    //         onCompleted: (response: any) => {
    //             const data = response.readOneAssetReporting_LogBookEntrySection
    //             setAssetReporting(data)
    //         },
    //         onError: (error: any) => {
    //             console.error('AssetReporting_LogBookEntrySection error', error)
    //         },
    //     },
    // )

    const getLogBookConfig = () => {
        if (lastSnapshot) {
            getLogBookEntryOldConfig({
                variables: {
                    id: lastSnapshot,
                },
            })
        }
    }

    const handleSetLogbook = async (logbook: any) => {
        setFuelLogs(logbook.fuelLog.nodes)
        if (+logbook.logBookID > 0) {
            if (offline) {
                const data = await clbcModel.getByCustomisedLogBookId(
                    logbook.logBookID,
                )
                setLogBookConfig(data)
            } else {
                locked
                    ? getLogBookConfig()
                    : queryLogBookConfig({
                          variables: {
                              id: logbook.logBook.id,
                          },
                      })
            }
        }
        setLogbook(logbook)
        setStartDate(logbook.startDate)
        setMasterID(logbook.masterID)
        logbook.state === 'Locked' ? setLocked(true) : setLocked(false)
        const sectionTypes = Array.from(
            new Set(
                logbook.logBookEntrySections.nodes.map(
                    (sec: any) => sec.className,
                ),
            ),
        ).map((type) => ({
            className: type,
            ids: logbook.logBookEntrySections.nodes
                .filter((sec: any) => sec.className === type)
                .map((sec: any) => sec.id),
        }))
        setLogEntrySections(sectionTypes)
        sectionTypes.forEach(async (section: any) => {
            // if (section.className === 'SeaLogs\\Engine_LogBookEntrySection') {
            //     if (offline) {
            //         const data = await engineModel.getByIds(section.ids)
            //         setEngine(data)
            //     } else {
            //         getSectionEngine_LogBookEntrySection({
            //             variables: {
            //                 id: section.ids,
            //             },
            //         })
            //     }
            // }
            if (section.className === 'SeaLogs\\Fuel_LogBookEntrySection') {
                if (offline) {
                    const data = await fuelModel.getByIds(section.ids)
                    setFuel(data)
                } else {
                    getSectionFuel_LogBookEntrySection({
                        variables: {
                            id: section.ids,
                        },
                    })
                }
            }
            // if (section.className === 'SeaLogs\\Ports_LogBookEntrySection') {
            //     if (offline) {
            //         const data = await portModel.getByIds(section.ids)
            //         setPorts(data)
            //     } else {
            //         getSectionPorts_LogBookEntrySection({
            //             variables: {
            //                 id: section.ids,
            //             },
            //         })
            //     }
            // }
            if (
                section.className ===
                'SeaLogs\\VesselDailyCheck_LogBookEntrySection'
            ) {
                if (offline) {
                    const data = await vesselDailyCheckModel.getByIds(
                        section.ids,
                    )
                    setVesselDailyCheck(data)
                } else {
                    getSectionVesselDailyCheck_LogBookEntrySection({
                        variables: {
                            id: section.ids,
                        },
                    })
                }
            }
            if (
                section.className ===
                'SeaLogs\\LogBookSignOff_LogBookEntrySection'
            ) {
                if (offline) {
                    const data = await signOffModel.getByIds(section.ids)
                    setSignOff(data)
                } else {
                    getLogBookSignOff_LogBookEntrySection({
                        variables: {
                            id: section.ids,
                        },
                    })
                }
            }

            if (
                section.className === 'SeaLogs\\CrewWelfare_LogBookEntrySection'
            ) {
                if (offline) {
                    const data = await crewWelfareModel.getByIds(section.ids)
                    setCrewWelfare(data[0])
                } else {
                    getSectionCrewWelfare_LogBookEntrySection({
                        variables: {
                            id: section.ids,
                        },
                    })
                }
            }
            // if (
            //     section.className ===
            //     'SeaLogs\\VoyageSummary_LogBookEntrySection'
            // ) {
            //     if (offline) {
            //         const data = await voyageSummaryModel.getByIds(section.ids)
            //         setVoyageSummary(data)
            //     } else {
            //         getSectionVoyageSummary_LogBookEntrySection({
            //             variables: {
            //                 id: section.ids,
            //             },
            //         })
            //     }
            // }
            if (
                section.className === 'SeaLogs\\TripReport_LogBookEntrySection'
            ) {
                if (offline) {
                    let data = await tripReportModel.getByIds(section.ids)
                    data = await addTripEventRelationships(data)
                    setTripReport(data)
                    await refreshVesselPosition(data)
                } else {
                    getSectionTripReport_LogBookEntrySection({
                        variables: {
                            id: section.ids,
                        },
                    })
                }
            }
            if (
                section.className === 'SeaLogs\\CrewMembers_LogBookEntrySection'
            ) {
                if (offline) {
                    const data = await cmlbsModel.getByIds(section.ids)
                    setCrewMembers(data)
                } else {
                    const searchFilter: SearchFilter = {}
                    searchFilter.id = { in: section.ids }
                    getSectionCrewMembers_LogBookEntrySection({
                        variables: {
                            filter: searchFilter,
                        },
                    })
                }
            }
            // if (
            //     section.className ===
            //     'SeaLogs\\CrewTraining_LogBookEntrySection'
            // ) {
            //     if (offline) {
            //         const data = await ctlbesModel.getByIds(section.ids)
            //         setCrewTraining(data)
            //     } else {
            //         getSectionCrewTraining_LogBookEntrySection({
            //             variables: {
            //                 id: section.ids,
            //             },
            //         })
            //     }
            // }
            if (
                section.className ===
                'SeaLogs\\Supernumerary_LogBookEntrySection'
            ) {
                if (offline) {
                    const data = await supernumeraryModel.getByIds(section.ids)
                    setSupernumerary(data)
                } else {
                    getSectionSupernumerary_LogBookEntrySection({
                        variables: {
                            id: section.ids,
                        },
                    })
                }
            }
            // if (section.className === 'SeaLogs\\Engineer_LogBookEntrySection') {
            //     if (offline) {
            //         const data = await elbesModel.getByIds(section.ids)
            //         setEngineer(data)
            //     } else {
            //         getSectionEngineer_LogBookEntrySection({
            //             variables: {
            //                 id: section.ids,
            //             },
            //         })
            //     }
            // }
            // if (
            //     section.className ===
            //     'SeaLogs\\AssetReporting_LogBookEntrySection'
            // ) {
            //     if (offline) {
            //         const data = await arlbesModel.getByIds(section.ids)
            //         setAssetReporting(data)
            //     } else {
            //         getSectionAssetReporting_LogBookEntrySection({
            //             variables: {
            //                 id: section.ids,
            //             },
            //         })
            //     }
            // }
        })
        setLoaded(true)
    }

    const getLogBookEntryByID = async (id: number) => {
        if (offline) {
            const data = await logbookModel.getById(id)
            if (data) {
                handleSetLogbook(data)
            }
        } else {
            queryLogBookEntry({
                variables: {
                    logbookEntryId: +id,
                },
            })
        }
    }

    getVesselByID(vesselID, setVessel, offline)
    /* const getVesselByID = async () => {
        const vessel = await vesselModel.getById(vesselID.toString())
        if (vessel) {
            setVessel(vessel)
        }
    } */

    getOneClient(setClient, offline)
    /* const getOneClient = async () => {
        const c = await cModel.getById(localStorage.getItem('clientId') ?? 0)
        setClient(c)
    } */

    getSeaLogsMembersList(setCrew, offline)
    // const getSeaLogsMembersList = async () => {
    //     const crew = await slmModel.getAll()
    //     if (crew) {
    //         setCrew(crew)
    //     }
    // }

    const [queryPrevSectionMemberComments] = useLazyQuery(
        ReadSectionMemberComments,
        {
            fetchPolicy: 'cache-and-network',
            onCompleted: (response: any) => {
                const data = response.readSectionMemberComments.nodes
                if (data) {
                    // Set previous comments
                    if (logbook.state !== 'Locked') {
                        setPrevComments(data)
                    }
                }
            },
            onError: (error: any) => {
                console.error('queryPrevSectionMemberComments error', error)
            },
        },
    )

    const handleSetPrevLogbooks = async (prevLogbooks: any) => {
        const sectionIDs = prevLogbooks.flatMap((prevLogbook: any) => {
            const sections = prevLogbook.logBookEntrySections.nodes.filter(
                (item: any) =>
                    item.className ===
                    'SeaLogs\\LogBookSignOff_LogBookEntrySection',
            )
            if (sections && sections.length > 0) {
                return sections.map((section: any) => section.id)
            }
            return []
        })
        if (vessel?.displayLogbookComments) {
            if (offline) {
                const data = await smcModel.getPreviousComments(sectionIDs)
                if (data) {
                    // Set previous comments
                    if (logbook.state !== 'Locked') {
                        setPrevComments(data)
                    }
                }
            } else {
                await queryPrevSectionMemberComments({
                    variables: {
                        filter: {
                            logBookEntrySectionID: { in: sectionIDs },
                            hideComment: { eq: false },
                            commentType: { eq: 'Section' },
                            comment: { ne: null },
                        },
                    },
                })
            }
        }
    }

    const date_params = useMemo(
        () => ({
            disable: false,
            startLabel: 'Start date',
            endLabel: 'End date',
            startDate: logbook?.startDate,
            endDate: logbook?.endDate,
            handleStartDateChange: false,
            handleEndDateChange: false,
            showOvernightCheckbox: false,
            showEndDate: logbook?.endDate ?? false,
            handleShowEndDat: false,
        }),
        [logbook],
    )

    const [updateLogbookEntry] = useMutation(UPDATE_LOGBOOK_ENTRY, {
        onError: (error: any) => {
            toast.error('Failed to update logbook entry')
        },
    })

    const handleSetStartDate = async (date: any) => {
        setStartDate(date)
        if (offline) {
            const data = await logbookModel.save({
                id: logbook.id,
                startDate: dayjs(date).format('YYYY-MM-DD'),
            })
            setLogbook(data)
        } else {
            updateLogbookEntry({
                variables: {
                    input: {
                        id: logentryID,
                        startDate: date,
                    },
                },
            })
        }
    }

    const handleSetEndDate = async (date: any) => {
        // setEndDate(date)
        if (offline) {
            const data = await logbookModel.save({
                id: logbook.id,
                endDate: dayjs(date).format('YYYY-MM-DD'),
            })
            setLogbook(data)
        } else {
            updateLogbookEntry({
                variables: {
                    input: {
                        id: logentryID,
                        endDate: date,
                    },
                },
            })
        }
    }

    const [updateReloadLogbookEntry] = useMutation(UPDATE_LOGBOOK_ENTRY, {
        onCompleted: (response: any) => {
            // Force a fresh fetch from the server to get updated master data
            queryLogBookEntry({
                variables: {
                    logbookEntryId: +logentryID,
                },
                fetchPolicy: 'no-cache',
            })
        },
        onError: (error: any) => {
            toast.error('Failed to update logbook entry')
            console.error('Logbook entry update error', error)
        },
    })

    const handleSetMaster = async (master: any) => {
        // setMaster(master)
        const masterID = master ? master.value : null
        setMasterID(masterID)
        if (offline) {
            if (master) {
                const member = await slmModel.getById(master.value)
                const data = await logbookModel.save({
                    id: logbook.id,
                    masterID: master.value,
                    master: member,
                })
                setLogbook(data)
            } else {
                // Clear master
                const data = await logbookModel.save({
                    id: logbook.id,
                    masterID: null,
                    master: null,
                })
                setLogbook(data)
            }
        } else {
            updateReloadLogbookEntry({
                variables: {
                    input: {
                        id: logentryID,
                        masterID: masterID,
                    },
                },
            })
        }
    }

    const [permissions, setPermissions] = useState<any>(false)
    const [edit_logBookEntry, setEdit_logBookEntry] = useState(false)

    const init_permissions = () => {
        if (permissions) {
            if (
                hasPermission(
                    process.env.EDIT_LOGBOOKENTRY || 'EDIT_LOGBOOKENTRY',
                    permissions,
                )
            ) {
                setEdit_logBookEntry(true)
            } else {
                setEdit_logBookEntry(false)
            }
        }
    }

    useEffect(() => {
        setPermissions(getPermissions)
        init_permissions()
    }, [])

    useEffect(() => {
        init_permissions()
    }, [permissions])

    const releaseLockState = async () => {
        if (!edit_logBookEntry) {
            toast.error('You do not have permission to unlock this log entry')
            return
        }
        if (openLogEntries?.length > 0) {
            toast.error(
                <div>
                    Please close log entry for{' '}
                    <Link
                        href={`/log-entries?vesselID=${vesselID}&logentryID=${openLogEntries[0].id}`}>
                        <span className="underline">
                            {formatDate(openLogEntries[0].startDate)}
                        </span>
                    </Link>{' '}
                    before unlocking.
                </div>,
            )
        } else {
            setLocked(false)
            if (offline) {
                const data = await logbookModel.save({
                    id: logbook.id,
                    state: 'Reopened',
                })
                setLogbook(data)
            } else {
                updateLogbookEntry({
                    variables: {
                        input: {
                            id: logentryID,
                            state: 'Reopened',
                        },
                    },
                })
            }
        }
    }

    const updateSignOff = async (signOff: any) => {
        if (offline) {
            const data = await signOffModel.getByIds([signOff.id])
            setSignOff(data)
        } else {
            getLogBookSignOff_LogBookEntrySection({
                variables: {
                    id: [signOff.id],
                },
            })
        }
    }

    const [createAppNotification] = useMutation(CreateAppNotification, {
        onCompleted: (response: any) => {
            // console.log('createAppNotification', response)
        },
        onError: (error: any) => {
            // console.error('createAppNotification error', error)
        },
    })

    const updateTripReport = async (tripReportData: any) => {
        if (offline) {
            let data = await tripReportModel.getByIds(tripReportData.id)
            data = await addTripEventRelationships(data)
            setTripReport(data)
            await refreshVesselPosition(data)
        } else {
            if (tripReportData.id.length > 0) {
                getSectionTripReport_LogBookEntrySection({
                    variables: {
                        id: tripReportData.id,
                    },
                })
            } else {
                setTripReport([])
                refreshVesselPosition([])
            }
        }
        if (tripReportData.key == 'dangerousGoodsChecklistID') {
            const updatedTripReport = tripReport.map((trip: any) => {
                if (trip.id == tripReportData.currentTripID) {
                    return {
                        ...trip,
                        dangerousGoodsChecklist: {
                            id: tripReportData.value,
                        },
                    }
                }
                return trip
            })
            setTripReport(updatedTripReport)
        }
        if (tripReportData.key == 'departTime') {
            const updatedTripReport = tripReport.map((trip: any) => {
                if (trip.id == tripReportData.currentTripID) {
                    return {
                        ...trip,
                        departTime: tripReportData.value,
                    }
                }
                return trip
            })
            // Save vessel departure notification
            const currentTrip = updatedTripReport.find(
                (trip: any) => trip.id == tripReportData.currentTripID,
            )
            if (currentTrip) {
                const relativeUrl = `${pathname}?${searchParams.toString()}`
                const input = {
                    title: 'Vessel Departure',
                    message:
                        currentTrip.fromLocation &&
                        currentTrip.fromLocation.title
                            ? `Vessel ${vessel.title} departed from ${currentTrip.fromLocation.title} at ${currentTrip.departTime}.`
                            : `Vessel ${vessel.title} departed at ${currentTrip.departTime}.`,
                    moduleName: 'TripReport_LogBookEntrySection',
                    moduleID: +currentTrip.id,
                    targetLink: relativeUrl,
                    notificationType: 'vesselDeparture',
                    deliveryMethods: 'app',
                }
                createAppNotification({
                    variables: {
                        input,
                    },
                })
            }
            setTripReport(updatedTripReport)
        }
        if (tripReportData.key == 'totalVehiclesCarried') {
            const updatedTripReport = tripReport.map((trip: any) => {
                if (trip.id == tripReportData.currentTripID) {
                    return {
                        ...trip,
                        totalVehiclesCarried: tripReportData.value,
                    }
                }
                return trip
            })
            setTripReport(updatedTripReport)
        }
        if (tripReportData.key == 'arriveTime') {
            const updatedTripReport = tripReport.map((trip: any) => {
                if (trip.id == tripReportData.currentTripID) {
                    return {
                        ...trip,
                        arriveTime: tripReportData.value,
                    }
                }
                return trip
            })

            // Save late vessel arrival notification
            const currentTrip = updatedTripReport.find(
                (trip: any) => trip.id == tripReportData.currentTripID,
            )
            if (
                currentTrip &&
                isVesselArrivalLate(currentTrip.arriveTime, currentTrip.arrive)
            ) {
                const relativeUrl = `${pathname}?${searchParams.toString()}`
                const input = {
                    title: 'Late Vessel Arrival',
                    message:
                        currentTrip.toLocation && currentTrip.toLocation.title
                            ? `Vessel ${vessel.title} arrived late at ${currentTrip.toLocation.title}. The actual arrival time was ${currentTrip.arrive} and the expected arrival time was ${currentTrip.arriveTime}.`
                            : `Vessel ${vessel.title} arrived late. The actual arrival time was ${currentTrip.arrive} and the expected arrival time was ${currentTrip.arriveTime}.`,
                    moduleName: 'TripReport_LogBookEntrySection',
                    moduleID: +currentTrip.id,
                    targetLink: relativeUrl,
                    notificationType: 'lateVesselArrival',
                    deliveryMethods: 'app',
                }
                createAppNotification({
                    variables: {
                        input,
                    },
                })
            }

            setTripReport(updatedTripReport)
        }
        if (tripReportData.key == 'arrive') {
            const updatedTripReport = tripReport.map((trip: any) => {
                if (trip.id == tripReportData.currentTripID) {
                    return {
                        ...trip,
                        arrive: tripReportData.value,
                    }
                }
                return trip
            })

            // Save late vessel arrival notification
            const currentTrip = updatedTripReport.find(
                (trip: any) => trip.id == tripReportData.currentTripID,
            )
            if (
                currentTrip &&
                isVesselArrivalLate(currentTrip.arriveTime, currentTrip.arrive)
            ) {
                const relativeUrl = `${pathname}?${searchParams.toString()}`
                const input = {
                    title: 'Late Vessel Arrival',
                    message:
                        currentTrip.toLocation && currentTrip.toLocation.title
                            ? `Vessel ${vessel.title} arrived late at ${currentTrip.toLocation.title}. The actual arrival time was ${currentTrip.arrive} and the expected arrival time was ${currentTrip.arriveTime}.`
                            : `Vessel ${vessel.title} arrived late. The actual arrival time was ${currentTrip.arrive} and the expected arrival time was ${currentTrip.arriveTime}.`,
                    moduleName: 'TripReport_LogBookEntrySection',
                    moduleID: +currentTrip.id,
                    targetLink: relativeUrl,
                    notificationType: 'lateVesselArrival',
                    deliveryMethods: 'app',
                }
                createAppNotification({
                    variables: {
                        input,
                    },
                })
            }

            setTripReport(updatedTripReport)
        }
        if (tripReportData.key == 'pob') {
            const updatedTripReport = tripReport.map((trip: any) => {
                if (trip.id == tripReportData.currentTripID) {
                    return {
                        ...trip,
                        pob: tripReportData.value,
                    }
                }
                return trip
            })
            setTripReport(updatedTripReport)
        }
        if (tripReportData.key == 'comment') {
            const updatedTripReport = tripReport.map((trip: any) => {
                if (trip.id == tripReportData.currentTripID) {
                    return {
                        ...trip,
                        comment: tripReportData.value,
                    }
                }
                return trip
            })
            setTripReport(updatedTripReport)
        }
        if (tripReportData.key == 'fromLocationID') {
            let fromLocation = {
                title: tripReportData.label,
                id: tripReportData.value,
            }
            if (offline) {
                fromLocation = await geoLocationModel.getById(
                    tripReportData.value,
                )
            }
            const updatedTripReport = tripReport.map((trip: any) => {
                if (trip.id == tripReportData.currentTripID) {
                    return {
                        ...trip,
                        fromLocationID: tripReportData.value,
                        fromLocation: fromLocation,
                    }
                }
                return trip
            })
            setTripReport(updatedTripReport)
        }
        if (tripReportData.key == 'fromLocation') {
            const updatedTripReport = tripReport.map((trip: any) => {
                if (trip.id == tripReportData.currentTripID) {
                    return {
                        ...trip,
                        fromLat: tripReportData.latitude,
                        fromLong: tripReportData.longitude,
                        fromLocationID: 0,
                    }
                }
                return trip
            })
            setTripReport(updatedTripReport)
        }
        if (tripReportData.key == 'toLocationID') {
            let toLocation = {
                title: tripReportData.label,
                id: tripReportData.value,
            }
            if (offline) {
                toLocation = await geoLocationModel.getById(
                    tripReportData.value,
                )
            }
            const updatedTripReport = tripReport.map((trip: any) => {
                if (trip.id == tripReportData.currentTripID) {
                    return {
                        ...trip,
                        toLocationID: tripReportData.value,
                        toLocation: toLocation,
                    }
                }
                return trip
            })
            setTripReport(updatedTripReport)
        }
        if (tripReportData.key == 'toLocation') {
            const updatedTripReport = tripReport.map((trip: any) => {
                if (trip.id == tripReportData.currentTripID) {
                    return {
                        ...trip,
                        toLat: tripReportData.latitude,
                        toLong: tripReportData.longitude,
                        toLocationID: 0,
                    }
                }
                return trip
            })
            setTripReport(updatedTripReport)
        }
    }

    const updateFuel = async (fuel: any) => {
        if (offline) {
            const data = await fuelModel.getByIds([fuel.id])
            setFuel(data)
        } else {
            getSectionFuel_LogBookEntrySection({
                variables: {
                    id: [fuel.id],
                },
            })
        }
    }

    const updateCrewWelfare = async (crewWelfare: any) => {
        if (offline) {
            const data = await crewWelfareModel.getById(crewWelfare.id)
            setCrewWelfare(data)
        } else {
            getSectionCrewWelfare_LogBookEntrySection({
                variables: {
                    id: [crewWelfare.id],
                },
            })
        }
    }

    GetLogBookEntriesMembers(handleSetCrewMembers, offline)
    const [queryLogBookEntry] = useLazyQuery(ReadOneLogBookEntry, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response: any) => {
            const data = response.readOneLogBookEntry
            if (data) {
                handleSetLogbook(data)
            }
        },
        onError: (error: any) => {
            console.error('queryLogBookEntry error', error)
        },
    })

    useEffect(() => {
        getLogBookEntryByID(logentryID)
    }, [])

    const addTripEventRelationships = async (trips: any) => {
        const newTripReport = await Promise.all(
            trips.map(async (trip: any) => {
                let events = trip.tripEvents.nodes
                events = await Promise.all(
                    events.map(async (event: any) => {
                        if (+event.supernumeraryID > 0) {
                            // supernumerary
                            event.supernumerary =
                                (await supernumeraryEventModel.getById(
                                    event.supernumeraryID,
                                )) || {}
                        } else if (
                            +event.eventType_PassengerDropFacilityID > 0
                        ) {
                            // eventType_PassengerDropFacility

                            event.eventType_PassengerDropFacility =
                                (await passengerDropFacilityModel.getById(
                                    event.eventType_PassengerDropFacilityID,
                                )) || {}
                        } else if (+event.eventType_BarCrossingID > 0) {
                            // eventType_BarCrossing
                            event.eventType_BarCrossing =
                                (await barCrossingModel.getById(
                                    event.eventType_BarCrossingID,
                                )) || {}
                        } else if (
                            +event.eventType_RestrictedVisibilityID > 0
                        ) {
                            // eventType_RestrictedVisibility
                            event.eventType_RestrictedVisibility =
                                (await restrictedVisibilityModel.getById(
                                    event.eventType_RestrictedVisibilityID,
                                )) || {}
                        } else if (+event.eventType_TaskingID > 0) {
                            // eventType_Tasking
                            event.eventCategory = 'Tasking'
                            event.eventType_Tasking =
                                (await taskingModel.getById(
                                    event.eventType_TaskingID,
                                )) || {}
                        } else if (+event.infringementNoticeID > 0) {
                            // infringementNotice
                            event.infringementNotice =
                                (await infringementNoticeModel.getById(
                                    event.infringementNoticeID,
                                )) || {}
                        }
                        return event
                    }),
                )
                // dangerousGoodsChecklist
                let dgcl = trip.dangerousGoodsChecklist
                if (+trip.dangerousGoodsChecklistID > 0) {
                    dgcl = await dangerousGoodsChecklistModel.getById(
                        trip.dangerousGoodsChecklistID,
                    )
                }
                // tripReport_Stops
                const tripReport_Stops =
                    await tripReport_StopModel.getByFieldID(
                        'logBookEntrySectionID',
                        trip.id,
                    )
                return {
                    ...trip,
                    tripEvents: { nodes: events },
                    dangerousGoodsChecklist: dgcl,
                    tripReport_Stops: { nodes: tripReport_Stops },
                }
            }),
        )
        return newTripReport
    }

    const [deleteLogBookEntry] = useMutation(DELETE_LOGBOOK_ENTRY, {
        onCompleted: (response: any) => {
            router.push(`/vessel/info?id=${vesselID}`)
        },
        onError: (error: any) => {
            console.error('Logbook entry delete error', error)
        },
    })

    const handleDeleteLBE = async () => {
        deleteLogBookEntry({
            variables: {
                ids: [+logentryID],
            },
        })
    }

    useEffect(() => {
        if (
            typeof window !== 'undefined' &&
            typeof window.localStorage !== 'undefined'
        ) {
            const result = localStorage.getItem('admin')
            const admin = result === 'true'
            setIsAdmin(admin)
        }
    }, [])

    const menuItems = [
        { label: 'Crew manifest', value: 'crew' },
        { label: 'Pre-departure checks', value: 'pre-departure-checks' },
        { label: 'Weather', value: 'weather' },
        { label: 'Trip logs', value: 'trip-log' },
        { label: 'Complete log entry', value: 'complete-logbook' },
    ]

    return (
        <>
            <ListHeader
                icon={<VesselIcon vessel={vessel} />}
                title={vessel && vessel?.title}
                titleClassName=" leading-none"
                actions={
                    <LogbookActionMenu
                        items={menuItems}
                        onBack={() => router.back()}
                        onDistructAction={() => setDeleteConfirmation(true)}
                        ShowDistructive={
                            isAdmin ||
                            (!locked &&
                                permissions &&
                                hasPermission(
                                    'DELETE_LOGBOOKENTRY',
                                    permissions,
                                ))
                        }
                        setOpen={setOpen}
                        logBookConfig={logBookConfig}
                        disTructLabel="Delete logbook entry"
                        openCreateTaskSidebar={openCreateTaskSidebar}
                        setOpenCreateTaskSidebar={setOpenCreateTaskSidebar}
                        forecast={forecast}
                        setForecast={setForecast}
                        logBookEntryID={logbook.id}
                        setIsWriteModeForecast={setIsWriteModeForecast}
                        openTripSelectionDialog={openTripSelectionDialog}
                        setOpenTripSelectionDialog={setOpenTripSelectionDialog}
                        doCreateTripReport={doCreateTripReport}
                    />
                }
            />
            <div className="pt-16 space-y-8">
                <div className={`space-y-6`}>
                    {logbook && (
                        <>
                            <div
                                className={cn(
                                    'grid gap-8 md:grid-cols-2',
                                    locked
                                        ? 'opacity-90 pointer-events-none'
                                        : '',
                                )}>
                                <LogDate
                                    log_params={date_params}
                                    setStartDate={handleSetStartDate}
                                    setEndDate={handleSetEndDate}
                                    edit_logBookEntry={edit_logBookEntry}
                                />
                                <MasterList
                                    offline={offline}
                                    master={logbook?.master ?? {}}
                                    masterTerm={client?.masterTerm ?? 'Master'}
                                    setMaster={handleSetMaster}
                                    crewMembers={crewMembers}
                                    edit_logBookEntry={edit_logBookEntry}
                                />
                            </div>
                        </>
                    )}

                    {locked && (
                        <div className="flex-1 flex flex-wrap gap-2 justify-between">
                            <div className="flex items-center gap-2">
                                <span className="font-medium">Completed</span>
                                <span className="font-semibold">
                                    {logbook?.lockedDate
                                        ? format(logbook?.lockedDate, 'PPpp')
                                        : ''}
                                </span>
                            </div>

                            {!offline && (
                                <div className="flex gap-3">
                                    {loaded && locked && (
                                        <Button
                                            iconLeft={UnlockIcon}
                                            onClick={releaseLockState}
                                            variant="secondary">
                                            Unlock
                                        </Button>
                                    )}

                                    <Button iconLeft={File} asChild>
                                        <Link
                                            href={`/log-entries/pdf?vesselID=${vesselID}&logentryID=${logentryID}&pdf`}>
                                            PDF (Latest){' '}
                                        </Link>
                                    </Button>
                                </div>
                            )}
                        </div>
                    )}
                </div>

                {lbeVersions && lbeVersions.length > 0 && (
                    <Card>
                        <CardHeader className="pb-2 ">
                            <CardTitle className="text-sm font-medium">
                                Previous Versions
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="flex flex-wrap gap-2">
                                {lbeVersions.map((version: any) => (
                                    <Button
                                        variant="secondary"
                                        size="sm"
                                        asChild>
                                        <Link
                                            key={version.id}
                                            href={`/log-entries/oldEntry?vesselID=${vesselID}&logentryID=${logentryID}&pdf&snapID=${version.id}`}>
                                            {dayjs(version.created).format(
                                                'DD/MM/YYYY H:m:s',
                                            )}
                                        </Link>
                                    </Button>
                                ))}
                            </div>
                        </CardContent>
                    </Card>
                )}

                <OpenPreviousLogbookComments
                    prevComments={prevComments}
                    onDismiss={(coms: any) => {
                        setPrevComments(coms)
                    }}
                    onDismissAll={() => setPrevComments([])}
                />
            </div>
            <div className="pt-24">
                <LogEntryMainContent
                    offline={offline}
                    logbook={logbook}
                    vessel={vessel}
                    loaded={loaded}
                    locked={locked}
                    logBookConfig={logBookConfig}
                    fuel={fuel}
                    updateFuel={updateFuel}
                    logentryID={logentryID}
                    logEntrySections={logEntrySections}
                    edit_logBookEntry={edit_logBookEntry}
                    supernumerary={supernumerary}
                    setSupernumerary={setSupernumerary}
                    crewMembers={crewMembers}
                    setCrewMembers={setCrewMembers}
                    crew={crew}
                    crewWelfare={crewWelfare}
                    updateCrewWelfare={updateCrewWelfare}
                    crewMembersList={crewMembersList}
                    masterID={masterID}
                    vesselDailyCheck={vesselDailyCheck}
                    setVesselDailyCheck={setVesselDailyCheck}
                    fuelLogs={fuelLogs}
                    signOff={signOff}
                    tripReport={tripReport}
                    updateSignOff={updateSignOff}
                    updateTripReport={updateTripReport}
                    client={client}
                    prevComments={prevComments}
                    setPrevComments={setPrevComments}
                    logBookStartDate={logbook?.startDate ?? startDate}
                    openCreateTaskSidebar={openCreateTaskSidebar}
                    setOpenCreateTaskSidebar={setOpenCreateTaskSidebar}
                    forecast={forecast}
                    setForecast={setForecast}
                    setIsWriteModeForecast={setIsWriteModeForecast}
                    isWriteModeForecast={isWriteModeForecast}
                    openTripSelectionDialog={openTripSelectionDialog}
                    setOpenTripSelectionDialog={setOpenTripSelectionDialog}
                    doCreateTripReport={doCreateTripReport}
                    setdDoCreateTripReport={setdDoCreateTripReport}
                />
            </div>
            <AlertDialogNew
                openDialog={deleteConfirmation}
                setOpenDialog={setDeleteConfirmation}
                handleCreate={handleDeleteLBE}
                title="Delete Logbook Entry"
                description="Are you sure you want to delete this logbook entry? This action cannot be undone."
                cancelText="Cancel"
                destructiveActionText="Delete"
                handleDestructiveAction={handleDeleteLBE}
                showDestructiveAction={true}
                variant="warning"
            />
            <RadioLogs open={open} setOpen={setOpen} logentryID={logentryID} />
        </>
    )
}
